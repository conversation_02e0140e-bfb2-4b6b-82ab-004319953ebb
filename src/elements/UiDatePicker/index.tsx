import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Calendar } from "lucide-react";

interface UiDatePickerProps {
  name: string;
  label: string;
  rules?: object;
  placeholder?: string;
  className?: string;
}

const UiDatePicker: React.FC<UiDatePickerProps> = ({
  name,
  label,
  rules,
  placeholder = "Select date",
  className = "",
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <div className="mb-4 relative">
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label}
      </label>
      <div className="relative">
        <Controller
          control={control}
          name={name}
          rules={rules}
          render={({ field }) => (
            <DatePicker
              id={name}
              selected={field.value ? new Date(field.value) : null}
              onChange={(date: Date | null) => field.onChange(date)}
              placeholderText={placeholder}
              className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className} ${
                errors[name] ? "border-red-500" : ""
              }`}
              dateFormat="MM/dd/yyyy HH:mm"
              showTimeSelect
              timeFormat="HH:mm"
              timeIntervals={15}
              timeCaption="Time"
              onKeyDown={(e) => e.preventDefault()}
              showPopperArrow={false} // Removes default arrow
            />
          )}
        />
        <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
      </div>
      {errors[name] && (
        <p className="mt-1 text-sm text-red-600">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
};

export default UiDatePicker;
