import React from "react";
import { type RegisterOptions, useFormContext } from "react-hook-form";
import { getNestedError } from "@/utils/getNestedError.ts";

interface UiInputProps {
  name: string;
  label?: string;
  type?: string;
  placeholder?: string;
  rules?: RegisterOptions;
  className?: string;
}

const UiInput: React.FC<UiInputProps> = ({
  name,
  label,
  type = "text",
  placeholder,
  rules,
  className = "",
}) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const error = getNestedError(errors, name);

  return (
    <div className="mb-4">
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label}
        </label>
      )}
      <input
        {...register(name, rules)}
        type={type}
        id={name}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className} ${
          error ? "border-red-500" : ""
        }`}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">
          {error.message as unknown as string}
        </p>
      )}
    </div>
  );
};

export default UiInput;
