import type { BaseItem } from "@/types/BaseTypes.ts";
import type { ILocation } from "@/types/Location.ts";
import type { IDomainValue } from "@/types/DomainValues.ts";

export const PremisesType = {
  AIRSIDE: "airside",
  LANDSIDE: "landside",
} as const;

export type PremisesType = (typeof PremisesType)[keyof typeof PremisesType];

export type IServiceType = BaseItem & {
  name: string;
};

export type IAddress = {
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  zipcode: string;
  latitude: number;
  longitude: number;
  locationId: ILocation;
  placeId: string;
};

export type IProperty = BaseItem & {
  serviceType: IServiceType;
  name: string;
  premisesType: PremisesType;
  address: IAddress;
  code: string;
  basePrice: number;
  logo: string;
};
export type IPropertyDetails = BaseItem & {
  propertyId: IProperty;
  aboutProperty: string;
  amenities: IDomainValue[];
  businessLogo: string;
  currency: IDomainValue;
  frontDeskClosingTime: string;
  frontDeskOpeningTime: string;
  isAllUnitsInSameAddress: boolean;
  isForExclusiveUse: boolean;
  maxReservationLength: number;
  minReservationLength: number;
  noOfReservationsPerPerson: number;
};
