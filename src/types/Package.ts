import type { BaseItem } from "@/types/BaseTypes.ts";
import type { IDomainValue } from "@/types/DomainValues.ts";
import type { IUnitType } from "@/types/UnitType.ts";
export type ExtraBed = {
  available: boolean;
  price: number;
};

export type IPackage = BaseItem & {
  extraBed?: ExtraBed;
  code: string;
  name: string;
  description: string;
  duration: number;
  propertyId: string;
  unitTypeId: IUnitType;
  price: number;
  taxes: IDomainValue[];
  amenities: IDomainValue[];
  noOfAdults: number;
  noOfChildren: number;
  rateCardPrice: number | null;
};
