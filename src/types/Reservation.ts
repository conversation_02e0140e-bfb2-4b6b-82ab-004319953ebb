import type { BaseItem } from "@/types/BaseTypes";
import type { IProperty, IPropertyDetails } from "@/types/Property";

// Phone number structure
export type IPhoneDetails = {
  countryCode: string;
  phoneNumber: string;
};

// Guest details
export type IGuestDetails = {
  firstName: string;
  lastName: string;
  email: string;
  phone: IPhoneDetails;
  gender: "male" | "female" | "other";
  _id?: string;
};

// Booker details (extends guest details)
export type IBookerDetails = IGuestDetails;

// Flight details
export type IFlightDetails = {
  number: string;
  from: string;
  to: string;
  arrivalDateTime: string;
  departureDateTime: string;
};

// Tax information
export type ITaxInfo = BaseItem & {
  name: string;
  value: string;
};

// Unit type information
export type IUnitType = BaseItem & {
  name: string;
};

// Package information
export type IPackage = BaseItem & {
  name: string;
};

// Reservation item (individual booking within a reservation)
export type IReservationItem = BaseItem & {
  flightDetails: IFlightDetails;
  bookerDetails: IBookerDetails;
  propertyId: string;
  couponDiscount: number;
  unitTypeId: IUnitType;
  packageId: IPackage;
  startDateTime: string;
  endDateTime: string;
  noOfAdults: number;
  noOfChildren: number;
  price: number;
  taxes: ITaxInfo[];
  tax: number;
  totalAmount: number;
  guestDetails: IGuestDetails[];
  specialRequest?: string;
  status: "blocked" | "confirmed" | "cancelled" | "completed";
  paymentStatus: "pending" | "paid" | "failed" | "refunded";
  active: boolean;
  deleted: boolean;
};

// Main reservation response structure
export type IReservationResponse = BaseItem & {
  bookerDetails: IBookerDetails;
  reservationCode: string;
  invoiceCode: string;
  propertyId: IProperty;
  items: IReservationItem[];
  subTotal: number;
  totalTax: number;
  grandTotal: number;
  status: "blocked" | "confirmed" | "cancelled" | "completed";
  paymentStatus: "pending" | "paid" | "failed" | "refunded";
  active: boolean;
  deleted: boolean;
  propertyDetails?: IPropertyDetails;
};

// Payment update request
export type IPaymentUpdateRequest = {
  status: "completed" | "failed" | "pending";
};

// Payment update response
export type IPaymentUpdateResponse = IReservationResponse;
