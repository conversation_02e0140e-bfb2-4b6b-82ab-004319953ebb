@import "tailwindcss";
@import "react-datepicker/dist/react-datepicker.css";

@theme{
    --color-primary: #2563EB;
    --color-primary-dark: #153885;
}

@layer base {
    body {
        @apply font-sans;
    }
}

@layer components {
    .btn-outline {
        @apply border border-primary text-primary px-4 py-2 rounded-md;
    }
    .btn-primary {
        @apply bg-gradient-to-r from-primary to-primary-dark text-white px-8 py-3 rounded-xl font-semibold  hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 focus:outline-none active:scale-[0.98];
    }
    .remove-icon{
        @apply [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-clear-button]:hidden appearance-none
    }
}

/* Airport Loader Animations */
@keyframes fly-across {
    0% {
        transform: translateX(-100px) rotate(45deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(200px) rotate(45deg);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-4px);
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animate-fly-across {
    animation: fly-across 3s ease-in-out infinite;
}

.animate-float {
    animation: float 2s ease-in-out infinite;
}

.animate-progress {
    animation: progress 2s ease-in-out infinite;
}

/* Package Card Animations */
@keyframes slideInUp {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutDown {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(20px);
        opacity: 0;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scaleOut {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

@keyframes pulse-success {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

@keyframes bounce-in {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

.animate-slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-slide-out-down {
    animation: slideOutDown 0.3s ease-in;
}

.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
}

.animate-scale-out {
    animation: scaleOut 0.2s ease-in;
}

.animate-pulse-success {
    animation: pulse-success 0.6s ease-out;
}

.animate-bounce-in {
    animation: bounce-in 0.5s ease-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.react-datepicker-wrapper{
    width: 100%;
}
.react-datepicker {
    display: flex !important;

}
.react-datepicker-popper{
    z-index: 999 !important;
    background: white !important;
}

.react-datepicker__time-box {
    z-index: 999 !important;
    max-height: 230px;
    overflow-y: auto;
}