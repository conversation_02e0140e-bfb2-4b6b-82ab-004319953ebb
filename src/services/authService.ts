import {
  signInWithPhoneN<PERSON>ber,
  signInWithPopup,
  GoogleAuthProvider,
  RecaptchaVerifier,
  type ConfirmationResult,
  type User,
  signOut,
  onAuthStateChanged,
  type UserCredential,
} from 'firebase/auth';
import { auth } from '@/config/firebase';
import axiosInstance from '@/config/base';

// Google Auth Provider
const googleProvider = new GoogleAuthProvider();

// Types
export interface AuthUser {
  uid: string;
  email: string | null;
  phoneNumber: string | null;
  displayName: string | null;
  photoURL: string | null;
  // Additional fields from your API
  id?: string;
  firstName?: string;
  lastName?: string;
}

export interface PhoneAuthResult {
  confirmationResult: ConfirmationResult;
}

export interface EmailAuthData {
  email: string;
  password: string;
}

export interface ApiAuthResponse {
  user: any;
  token?: string;
  message?: string;
}

export interface PhoneAuthData {
  phoneNumber: string;
  recaptchaVerifier: RecaptchaVerifier;
}

// Auth Service Class
class AuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;

  // Convert Firebase User to AuthUser
  private formatUser(user: User, apiUserData?: any): AuthUser {
    return {
      uid: user.uid,
      email: user.email,
      phoneNumber: user.phoneNumber,
      displayName: user.displayName,
      photoURL: user.photoURL,
      // Merge API user data if available
      ...apiUserData,
    };
  }

  // Send Firebase token to your API
  private async authenticateWithApi(firebaseToken: string, phoneData?: { phone: string; countryCode: string }): Promise<any> {
    const requestBody: any = { firebaseToken };

    // If phone data is provided, include it in the request
    if (phoneData) {
      requestBody.phone = phoneData;
    }

    const response = await axiosInstance.post('/authentication/firebase', requestBody);
    return response.data;
  }

  // Get user profile from your API
  async getUserProfile(): Promise<any> {
    const response = await axiosInstance.get('/authentication/profile');
    return response.data;
  }

  // Parse phone number to extract country code and phone number
  private parsePhoneNumber(fullPhoneNumber: string): { phone: string; countryCode: string } {
    // Remove any non-digit characters
    const cleanPhone = fullPhoneNumber.replace(/[^\d]/g, '');

    // Default to US if no country code detected
    if (cleanPhone.length === 10) {
      return {
        phone: cleanPhone,
        countryCode: '+1'
      };
    }

    // Extract country code for longer numbers
    if (cleanPhone.length > 10) {
      const countryCodeLength = cleanPhone.length - 10;
      return {
        phone: cleanPhone.slice(countryCodeLength),
        countryCode: `+${cleanPhone.slice(0, countryCodeLength)}`
      };
    }

    // Fallback for shorter numbers
    return {
      phone: cleanPhone,
      countryCode: '+1'
    };
  }

  // Email Authentication using your custom API
  async signInWithEmail({ email, password }: EmailAuthData): Promise<AuthUser> {
    try {
      // Use your custom API for email authentication
      const response = await axiosInstance.post('/authentication/login', {
        email,
        password,
      });

      // Assuming your API returns user data
      const apiUserData = response.data.data || response.data.user || response.data;

      // Create a mock Firebase user for consistency
      const mockUser = {
        uid: apiUserData.id || apiUserData._id || email,
        email: email,
        phoneNumber: null,
        displayName: apiUserData.firstName && apiUserData.lastName
          ? `${apiUserData.firstName} ${apiUserData.lastName}`
          : apiUserData.name || null,
        photoURL: apiUserData.photoURL || null,
      } as User;

      return this.formatUser(mockUser, apiUserData);
    } catch (error) {
      console.error('Email sign in error:', error);
      throw error;
    }
  }

  async signUpWithEmail({ email, password }: EmailAuthData): Promise<AuthUser> {
    // For now, redirect to sign in - you can implement signup API later
    return this.signInWithEmail({ email, password });
  }

  // Phone Authentication
  setupRecaptcha(containerId: string): RecaptchaVerifier {
    if (this.recaptchaVerifier) {
      this.recaptchaVerifier.clear();
    }
    
    this.recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
      size: 'invisible',
      callback: () => {
        // reCAPTCHA solved
      },
      'expired-callback': () => {
        // Response expired
      },
    });
    
    return this.recaptchaVerifier;
  }

  async sendPhoneVerification(phoneNumber: string, recaptchaVerifier: RecaptchaVerifier): Promise<ConfirmationResult> {
    return await signInWithPhoneNumber(auth, phoneNumber, recaptchaVerifier);
  }

  async verifyPhoneCode(confirmationResult: ConfirmationResult, code: string): Promise<AuthUser> {
    const userCredential = await confirmationResult.confirm(code);

    try {
      // Get Firebase token and send to your API
      const firebaseToken = await userCredential.user.getIdToken();

      // Parse phone number for API
      const phoneData = this.parsePhoneNumber(userCredential.user.phoneNumber || '');

      // Send Firebase token with phone data to your API
      await this.authenticateWithApi(firebaseToken, phoneData);

      // Get user profile from your API
      const profileData = await this.getUserProfile();

      return this.formatUser(userCredential.user, profileData.data || profileData);
    } catch (apiError) {
      console.warn('API authentication failed, using Firebase user only:', apiError);
      return this.formatUser(userCredential.user);
    }
  }

  // Google Authentication
  async signInWithGoogle(): Promise<AuthUser> {
    const userCredential: UserCredential = await signInWithPopup(auth, googleProvider);

    try {
      // Get Firebase token and send to your API (Google auth only sends token)
      const firebaseToken = await userCredential.user.getIdToken();
      await this.authenticateWithApi(firebaseToken); // No phone data for Google auth

      // Get user profile from your API
      const profileData = await this.getUserProfile();

      return this.formatUser(userCredential.user, profileData.data || profileData);
    } catch (apiError) {
      console.warn('API authentication failed, using Firebase user only:', apiError);
      return this.formatUser(userCredential.user);
    }
  }

  // Sign Out
  async signOut(): Promise<void> {
    await signOut(auth);
  }

  // Auth State Observer
  onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    return onAuthStateChanged(auth, (user) => {
      callback(user ? this.formatUser(user) : null);
    });
  }

  // Get Current User
  getCurrentUser(): AuthUser | null {
    const user = auth.currentUser;
    return user ? this.formatUser(user) : null;
  }

  // Clean up recaptcha
  cleanupRecaptcha(): void {
    if (this.recaptchaVerifier) {
      this.recaptchaVerifier.clear();
      this.recaptchaVerifier = null;
    }
  }
}

export const authService = new AuthService();
export default authService;
