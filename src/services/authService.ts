import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPhoneNumber,
  signInWithPopup,
  GoogleAuthProvider,
  RecaptchaVerifier,
  type ConfirmationResult,
  type User,
  signOut,
  onAuthStateChanged,
  type UserCredential,
} from 'firebase/auth';
import { auth } from '@/config/firebase';

// Google Auth Provider
const googleProvider = new GoogleAuthProvider();

// Types
export interface AuthUser {
  uid: string;
  email: string | null;
  phoneNumber: string | null;
  displayName: string | null;
  photoURL: string | null;
}

export interface PhoneAuthResult {
  confirmationResult: ConfirmationResult;
}

export interface EmailAuthData {
  email: string;
  password: string;
}

export interface PhoneAuthData {
  phoneNumber: string;
  recaptchaVerifier: RecaptchaVerifier;
}

// Auth Service Class
class AuthService {
  private recaptchaVerifier: RecaptchaVerifier | null = null;

  // Convert Firebase User to AuthUser
  private formatUser(user: User): AuthUser {
    return {
      uid: user.uid,
      email: user.email,
      phoneNumber: user.phoneNumber,
      displayName: user.displayName,
      photoURL: user.photoURL,
    };
  }

  // Email Authentication
  async signInWithEmail({ email, password }: EmailAuthData): Promise<AuthUser> {
    const userCredential: UserCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    return this.formatUser(userCredential.user);
  }

  async signUpWithEmail({ email, password }: EmailAuthData): Promise<AuthUser> {
    const userCredential: UserCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );
    return this.formatUser(userCredential.user);
  }

  // Phone Authentication
  setupRecaptcha(containerId: string): RecaptchaVerifier {
    if (this.recaptchaVerifier) {
      this.recaptchaVerifier.clear();
    }
    
    this.recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
      size: 'invisible',
      callback: () => {
        // reCAPTCHA solved
      },
      'expired-callback': () => {
        // Response expired
      },
    });
    
    return this.recaptchaVerifier;
  }

  async sendPhoneVerification(phoneNumber: string, recaptchaVerifier: RecaptchaVerifier): Promise<ConfirmationResult> {
    return await signInWithPhoneNumber(auth, phoneNumber, recaptchaVerifier);
  }

  async verifyPhoneCode(confirmationResult: ConfirmationResult, code: string): Promise<AuthUser> {
    const userCredential = await confirmationResult.confirm(code);
    return this.formatUser(userCredential.user);
  }

  // Google Authentication
  async signInWithGoogle(): Promise<AuthUser> {
    const userCredential: UserCredential = await signInWithPopup(auth, googleProvider);
    return this.formatUser(userCredential.user);
  }

  // Sign Out
  async signOut(): Promise<void> {
    await signOut(auth);
  }

  // Auth State Observer
  onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    return onAuthStateChanged(auth, (user) => {
      callback(user ? this.formatUser(user) : null);
    });
  }

  // Get Current User
  getCurrentUser(): AuthUser | null {
    const user = auth.currentUser;
    return user ? this.formatUser(user) : null;
  }

  // Clean up recaptcha
  cleanupRecaptcha(): void {
    if (this.recaptchaVerifier) {
      this.recaptchaVerifier.clear();
      this.recaptchaVerifier = null;
    }
  }
}

export const authService = new AuthService();
export default authService;
