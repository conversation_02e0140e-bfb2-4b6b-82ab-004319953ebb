import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "@/index.css";
import App from "@/App.tsx";
import { ToastProvider } from "@/components/toast/ToasterProvider";
import { PopupProvider } from "@/components/popup/PopupProvider";
import { AuthProvider } from "@/contexts/AuthContext";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AuthProvider>
      <ToastProvider>
        <PopupProvider>
          <App />
        </PopupProvider>
      </ToastProvider>
    </AuthProvider>
  </StrictMode>,
);
