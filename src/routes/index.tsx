import { createBrowserRouter } from "react-router";
import Layout from "@/layout";
import Home from "@/pages/home";
import SearchPage from "@/pages/search";
import DetailsPage from "@/pages/details";
import CheckoutPage from "@/pages/checkout";
import ReservationSuccess from "@/pages/reservation/success";
import ReservationFailed from "@/pages/reservation/failed";
import MyBooking from "@/pages/reservation";

const routes = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "search",
        element: <SearchPage />,
      },
      {
        path: "property/:propertyId",
        element: <DetailsPage />,
      },
      {
        path: "checkout",
        element: <CheckoutPage />,
      },
      {
        path: "reservation/success",
        element: <ReservationSuccess />,
      },
      {
        path: "reservation/failed",
        element: <ReservationFailed />,
      },
      {
        path: "my-reservations",
        element: <MyBooking />,
      },
    ],
  },
]);

export default routes;
