import axios, { type AxiosInstance } from "axios";
import environment from "@/constants/env.ts";

// Create an Axios instance with default configuration
const axiosInstance: AxiosInstance = axios.create({
  baseURL: `${environment.BASE_API_URL}/api/v1`,
  withCredentials: true,
  headers:
    import.meta.env.MODE === "development"
      ? {
          skip_zrok_interstitial: "true",
        }
      : {},
});

export default axiosInstance;
