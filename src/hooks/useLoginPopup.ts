import React from 'react';
import { usePopup } from '@/hooks/usePopup';
import LoginPopup from '@/components/auth/LoginPopup';

export const useLoginPopup = () => {
  const popup = usePopup();

  const openLoginPopup = (onSuccess?: () => void) => {
    popup.show(
      React.createElement(LoginPopup, { onSuccess }),
      {
        title: '',
        width: 480,
        closeOnOverlay: true,
        closeOnEsc: true,
      }
    );
  };

  return { openLoginPopup };
};
