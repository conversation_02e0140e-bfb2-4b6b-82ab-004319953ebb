import type { IPackage } from "@/types/Package.ts";

export type CheckoutLocationState = {
  selectedPackages: IPackage[];
  checkInDate: string;
  checkOutDate: string;
};

export type PhoneDetails = {
  phoneNumber: string;
  countryCode: string;
};

export type GuestDetails = {
  firstName: string;
  lastName: string;
  email: string;
  phone: PhoneDetails;
};

export type FlightDetails = {
  number: string;
  from: string;
  to: string;
  arrivalDateTime: string;
  departureDateTime: string;
};

export type ReservationItem = {
  packageId: string;
  startDateTime: string;
  endDateTime: string;
  noOfAdults: number;
  noOfChildren: number;
  guestDetails: GuestDetails[];
  flightDetails: FlightDetails;
  specialRequest?: string;
};

export type BookerDetails = {
  firstName: string;
  lastName: string;
  email: string;
  phone: PhoneDetails;
  gender: string;
};

export type ReservationRequest = {
  propertyId: string;
  paymentMethod: string;
  paymentProvider: string;
  currency: string;
  reservations: ReservationItem[];
  bookerDetails: BookerDetails;
};

export type ReservationResponse = {
  sessionId: string;
  sessionUrl: string;
};
