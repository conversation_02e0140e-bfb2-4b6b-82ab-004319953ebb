export type GuestFormValues = {
  guests: Guests[];
  flightNumber: string;
  fromLocation: string;
  toLocation: string;
  departureDateTime: string;
  arrivalDateTime: string;
  specialRequest?: string;
  gender?: string;
};

export type Guests = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
};

export type GuestFormProps = {
  guests: Guests[];
  onGuestChange: (_guests: Guests[]) => void;
};
