import GuestForm from "@/pages/checkout/guest-form";
import Header from "@/components/header";
import CheckoutCard from "@/components/checkout-card";
import type { GuestFormValues } from "@/pages/checkout/guest-form/type";
import { FormProvider, useForm } from "react-hook-form";
import { useLocation, useNavigate } from "react-router";
import type {
  CheckoutLocationState,
  ReservationRequest,
  ReservationResponse,
} from "@/pages/checkout/types.ts";
import useAxios from "@/hooks/useAxios";
import { useToast } from "@/hooks/useToast";
import { useEffect, useState } from "react";
import { getStripeInstance } from "@/utils/stripe-instance.ts";

const CheckoutPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = useToast();
  const { state } = location;
  const { selectedPackages, checkInDate, checkOutDate }: CheckoutLocationState =
    state;

  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: reservationData,
    loading: isReservationLoading,
    request: createReservation,
    error: reservationError,
  } = useAxios<ReservationResponse>();

  const methods = useForm<GuestFormValues>({
    defaultValues: {
      guests: [
        {
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        },
      ],
      flightNumber: "",
      fromLocation: "",
      toLocation: "",
      departureDateTime: "",
      arrivalDateTime: "",
      specialRequest: "",
    },
  });

  // Helper function to parse phone number
  const parsePhoneNumber = (phone: string) => {
    // Simple parsing - assumes format like "*************" or "4397864764"
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 10) {
      return { countryCode: "+1", phoneNumber: cleaned };
    } else if (cleaned.length > 10) {
      return {
        countryCode: `+${cleaned.slice(0, -10)}`,
        phoneNumber: cleaned.slice(-10),
      };
    }
    return { countryCode: "+1", phoneNumber: cleaned };
  };

  const onSubmit = async (data: GuestFormValues) => {
    if (!selectedPackages || selectedPackages.length === 0) {
      toast.show("No packages selected", { type: "error" });
      return;
    }

    setIsSubmitting(true);

    try {
      const propertyId = selectedPackages[0].propertyId;

      const reservationRequest: ReservationRequest = {
        propertyId,
        paymentMethod: "card",
        paymentProvider: "stripe",
        currency: "usd",
        reservations: selectedPackages.map((pkg) => ({
          packageId: pkg._id,
          startDateTime: checkInDate,
          endDateTime: checkOutDate,
          noOfAdults: pkg.noOfAdults,
          noOfChildren: pkg.noOfChildren,
          guestDetails: data.guests.map((guest) => ({
            firstName: guest.firstName,
            lastName: guest.lastName,
            email: guest.email,
            phone: parsePhoneNumber(guest.phone),
          })),
          flightDetails: {
            number: data.flightNumber,
            from: data.fromLocation,
            to: data.toLocation,
            arrivalDateTime: data.arrivalDateTime,
            departureDateTime: data.departureDateTime,
          },
          specialRequest: data.specialRequest || "",
        })),
        bookerDetails: {
          firstName: data.guests[0].firstName,
          lastName: data.guests[0].lastName,
          email: data.guests[0].email,
          phone: parsePhoneNumber(data.guests[0].phone),
          gender: data.gender || "other",
        },
      };

      await createReservation({
        url: "/reservations/block",
        method: "POST",
        data: reservationRequest,
      });

      toast.show("Reservation created successfully!", { type: "success" });
    } catch (error) {
      console.error("Reservation error:", error);
      toast.show(reservationError || "Failed to create reservation", {
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const handleStripeRedirect = async (sessionId: string) => {
      const stripe = await getStripeInstance();
      if (stripe) {
        const { error } = await stripe.redirectToCheckout({ sessionId });
        if (error) {
          toast.show(error.message || "Stripe redirect error", {
            type: "error",
          });
        }
      } else {
        toast.show("Stripe is not initialized", { type: "error" });
      }
    };
    if (reservationData) {
      handleStripeRedirect(reservationData.sessionId);
    }
  }, [reservationData, navigate]);

  return (
    <div>
      <Header />
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmit)}
          className="w-full bg-white"
        >
          <div className="px-6 py-4 flex flex-col md:flex-row gap-4 justify-between">
            <div className="w-full flex-3">
              <GuestForm />
            </div>
            <div className="w-full flex-1">
              <h1 className={"text-lg mb-2 font-medium"}>
                Your Services Summary
              </h1>
              <div className="space-y-4">
                {selectedPackages.map((pkg) => (
                  <CheckoutCard
                    key={pkg._id}
                    packageData={pkg}
                    checkIn={checkInDate}
                    checkOut={checkOutDate}
                  />
                ))}
              </div>

              {/* Submit Button */}
              <div className="mt-6">
                <button
                  type="submit"
                  disabled={isSubmitting || isReservationLoading}
                  className="w-full bg-primary text-white py-3 px-4 rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting || isReservationLoading
                    ? "Creating Reservation..."
                    : "Complete Booking"}
                </button>
              </div>
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default CheckoutPage;
