const Profile = () => {
  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
      <div className="flex flex-col items-center p-6">
        <div className={"flex gap-6"}>
          <div className="relative mb-4">
            <div className="w-15 h-15 rounded-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center text-xl font-bold text-blue-600 shadow-md">
              AV
            </div>
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          {/* User Info */}
          <div className=" mb-4">
            <h2 className="text-md font-semibold text-gray-900">
              <PERSON><PERSON>
            </h2>
            <p className="text-sm text-gray-500"><EMAIL></p>
            <p className="text-sm text-gray-500">+91 6282298765</p>
          </div>
        </div>

        {/* Edit Button */}
        <button className="w-full  bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium px-4 py-2 rounded-md flex items-center justify-center gap-2 transition-colors duration-200">
          Edit Profile
        </button>

        {/* Info Grid */}
        <div className="w-full grid grid-cols-2 gap-2 text-sm text-gray-600 mt-6">
          <div className="">Member Since</div>
          <div className="text-right">Aug 2024</div>
          <div className="">Total Bookings</div>
          <div className="text-right">4</div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
