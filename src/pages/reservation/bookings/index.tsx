import BookingItem from "@/components/booking-card";
import type { IReservationResponse } from "@/types";
import useAxios from "@/hooks/useAxios.tsx";
import { useEffect } from "react";
import AirportLoader from "@/components/loader";
import { useToast } from "@/hooks/useToast";

const Bookings = () => {
  const toast = useToast();
  const {
    data: bookings,
    loading: isBookingsLoading,
    request: getBookings,
    error: bookingsError,
  } = useAxios<IReservationResponse[]>();

  useEffect(() => {
    const getBookingsData = async () => {
      await getBookings({
        url: "/users/reservations",
        method: "GET",
      });
    };
    getBookingsData();
  }, []);

  useEffect(() => {
    if (bookingsError) {
      toast.show(bookingsError, { type: "error" });
    }
  }, [bookingsError]);

  if (isBookingsLoading) {
    return <AirportLoader message="Loading your bookings..." />;
  }

  return (
    <>
      <div className="container mx-auto p-4 px-10">
        <h2 className="text-2xl font-bold mb-4">My Bookings</h2>
        <div className="flex space-x-4 mb-4">
          <button className="bg-blue-500 text-white px-4 py-2 rounded">
            Upcoming
          </button>
          <button className="text-gray-600 px-4 py-2 rounded">Past</button>
        </div>
        {bookings &&
          bookings.map((booking, index) => (
            <BookingItem key={index} reservation={booking} />
          ))}
      </div>
    </>
  );
};

export default Bookings;
