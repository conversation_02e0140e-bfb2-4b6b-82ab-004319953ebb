import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";
import useAxios from "@/hooks/useAxios.tsx";
import { useToast } from "@/hooks/useToast";
import AirportLoader from "@/components/loader";
import Header from "@/components/header";
import type { IReservationResponse, IPaymentUpdateRequest } from "@/types";

function ReservationFailed() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const toast = useToast();
  const paymentId = searchParams.get("paymentId");
  const [error, setError] = useState<string | null>(null);

  const {
    data: paymentData,
    loading: isUpdating,
    request: updatePaymentStatus,
    error: updateError,
  } = useAxios<IReservationResponse>();

  useEffect(() => {
    const cancelPayment = async () => {
      try {
        const paymentData: IPaymentUpdateRequest = { status: "failed" };
        await updatePaymentStatus({
          url: `/payments/${paymentId}`,
          method: "PATCH",
          data: paymentData,
        });
      } catch (err) {
        console.error("Payment cancellation error:", err);
      }
    };

    if (paymentId) {
      cancelPayment();
    } else {
      setError("Payment ID not found");
    }
  }, [paymentId, updatePaymentStatus]);

  useEffect(() => {
    if (updateError) {
      setError(updateError);
      toast.show(updateError, { type: "error" });
    }
  }, [updateError, toast]);

  const handleGoHome = () => {
    navigate("/", { replace: true });
  };

  if (isUpdating) {
    return <AirportLoader message="Processing payment cancellation..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Header />

        <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
          <div className="max-w-md w-full">
            {/* Main Card */}
            <div className="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center">
              <div className="mb-6">
                <div className="mx-auto w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-10 h-10 text-red-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-2xl font-bold text-gray-800 mb-3">
                Payment Failed
              </h1>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {error ||
                  "We encountered an issue while processing your payment. Please try again or contact support."}
              </p>
              <p>Reservation Code: {paymentData?.reservationCode}</p>

              <button onClick={handleGoHome} className="btn-primary w-full">
                Go Home
              </button>
            </div>

            {/* Footer */}
            <div className="text-center mt-6 text-sm text-gray-500">
              Need help? Contact{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReservationFailed;
