import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";
import AirportLoader from "@/components/loader";
// import { invoiceTemplate } from '@/template/invoice';
import useAxios from "@/hooks/useAxios.tsx";
import { useToast } from "@/hooks/useToast";
import Header from "@/components/header";
import type { IPaymentUpdateRequest, IReservationResponse } from "@/types";

// Helper function to format currency as USD
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to format date and time
const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  }).format(date);
};

function ReservationSuccess() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const toast = useToast();
  const paymentId = searchParams.get("paymentId");
  const [error, setError] = useState<string | null>(null);

  const {
    data: bookingInfo,
    loading: isUpdating,
    request: updatePaymentStatus,
    error: updateError,
  } = useAxios<IReservationResponse>();

  useEffect(() => {
    const confirmPayment = async () => {
      try {
        const paymentData: IPaymentUpdateRequest = { status: "completed" };
        await updatePaymentStatus({
          url: `/payments/${paymentId}`,
          method: "PATCH",
          data: paymentData,
        });
        toast.show("Payment confirmed successfully!", { type: "success" });
      } catch (err) {
        console.error("Payment confirmation error:", err);
      }
    };

    if (paymentId) {
      confirmPayment();
    } else {
      setError("Payment ID not found");
    }
  }, [paymentId, updatePaymentStatus, toast]);

  useEffect(() => {
    if (updateError) {
      setError(updateError);
      toast.show(updateError, { type: "error" });
    }
  }, [updateError, toast]);

  const handleGoHome = () => {
    navigate("/", { replace: true });
  };

  if (isUpdating || !bookingInfo) {
    return <AirportLoader message="Finalizing your booking..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Header />
          <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
            <div className="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center">
              <h1 className="text-xl font-bold text-red-600 mb-3">Error</h1>
              <p className="text-gray-600 mb-4">{error}</p>
              <button onClick={handleGoHome} className="btn-primary">
                Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  const {
    reservationCode,
    bookerDetails,
    items,
    status,
    propertyId,
    paymentStatus,
    createdAt,
    subTotal,
    totalTax,
    grandTotal,
  } = bookingInfo;

  // Use the totals from the API response
  const totalAmount = grandTotal || 0;
  const baseFee = subTotal || 0;
  const taxAmount = totalTax || 0;

  const firstReservation = items?.[0];
  const lastReservation = items?.[items.length - 1];
  const guestNames =
    firstReservation?.guestDetails
      ?.map((guest) => `${guest.firstName} ${guest.lastName}`)
      .join(", ") || "N/A";

  // Group packages by type for display
  const packageCounts = Array.from(
    items
      .reduce((map, res) => {
        const id = res.packageId._id;
        map.set(id, {
          count: (map.get(id)?.count || 0) + 1,
          ...res,
        });
        return map;
      }, new Map())
      .values(),
  );

  // Temporarily commented out - will be re-enabled later
  // const handleInvoiceDownload = () => {
  //     const newWindow = window.open('', '_blank', 'width=1000,height=1000');
  //     if (newWindow) {
  //         newWindow.document.write(invoiceTemplate(bookingInfo));
  //         newWindow.document.close();
  //         newWindow.onload = () => {
  //             newWindow.focus();
  //             newWindow.print();
  //         };
  //     } else {
  //         alert('Pop-up blocked. Please allow pop-ups for this site.');
  //     }
  // };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Header />

        <div className="max-w-4xl mx-auto mt-8">
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-8">
            {/* Success Header */}
            <div className="text-center mb-8">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="w-8 h-8 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Booking Confirmed!
              </h2>
              <p className="text-gray-600 mb-4">
                You will soon receive a confirmation email at{" "}
                <span className="font-semibold text-primary">
                  {bookerDetails.email}
                </span>
              </p>
              <div className="bg-gray-50 rounded-lg p-4 inline-block">
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Booking ID:</span>{" "}
                  {reservationCode}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Booked on:</span>{" "}
                  {formatDateTime(new Date(createdAt ?? ""))}
                </p>
              </div>
            </div>

            {/* Property Details */}
            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Property Details
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span className="font-medium text-gray-800">
                      {propertyId.name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Address:</span>
                    <span className="font-medium text-gray-800">
                      {propertyId.address?.address1}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="font-medium text-gray-800 capitalize">
                      {status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment:</span>
                    <span className="font-medium text-green-600 capitalize">
                      {paymentStatus}
                    </span>
                  </div>
                </div>
              </div>

              {/* Guest Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Guest Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-gray-600">Guest Name:</span>
                    <p className="font-medium text-gray-800">{guestNames}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Email:</span>
                    <p className="font-medium text-gray-800">
                      {bookerDetails.email}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Phone:</span>
                    <p className="font-medium text-gray-800">
                      {bookerDetails.phone.countryCode}{" "}
                      {bookerDetails.phone.phoneNumber}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Check-in:</span>
                    <p className="font-medium text-gray-800">
                      {firstReservation
                        ? formatDateTime(
                            new Date(firstReservation.startDateTime),
                          )
                        : "N/A"}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Check-out:</span>
                    <p className="font-medium text-gray-800">
                      {lastReservation
                        ? formatDateTime(new Date(lastReservation.endDateTime))
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Room & Package Details */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Room & Package Details
              </h3>
              <div className="space-y-4">
                {packageCounts.map((reservation, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <p className="font-medium text-gray-800 mb-2">
                          {reservation.packageId.name}
                        </p>
                        <p className="text-sm text-gray-600">
                          Unit Type: {reservation.unitTypeId.name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {reservation.count} room(s)
                        </p>
                        <p className="text-sm text-gray-600">
                          {reservation.noOfAdults} adult(s),{" "}
                          {reservation.noOfChildren} child(ren)
                        </p>
                        <p className="text-sm font-medium text-primary mt-2">
                          {formatCurrency(reservation.totalAmount)}
                        </p>
                      </div>
                      {reservation.specialRequest && (
                        <div>
                          <p className="text-sm text-gray-600 font-medium">
                            Special Request:
                          </p>
                          <p className="text-sm text-gray-700">
                            {reservation.specialRequest}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Flight Details */}
            {firstReservation?.flightDetails && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Flight Information
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Flight Number:</p>
                      <p className="font-medium text-gray-800">
                        {firstReservation.flightDetails.number}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Route:</p>
                      <p className="font-medium text-gray-800">
                        {firstReservation.flightDetails.from} →{" "}
                        {firstReservation.flightDetails.to}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Departure:</p>
                      <p className="font-medium text-gray-800">
                        {formatDateTime(
                          new Date(
                            firstReservation.flightDetails.departureDateTime,
                          ),
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Arrival:</p>
                      <p className="font-medium text-gray-800">
                        {formatDateTime(
                          new Date(
                            firstReservation.flightDetails.arrivalDateTime,
                          ),
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Payment Summary */}
          <div className="border-t border-gray-200 pt-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Payment Summary
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Base Fee:</span>
                <span className="font-medium">{formatCurrency(baseFee)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Service Fee & Taxes:</span>
                <span className="font-medium">{formatCurrency(taxAmount)}</span>
              </div>
              <div className="border-t border-gray-300 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-800">
                    Grand Total:
                  </span>
                  <span className="text-lg font-bold text-primary">
                    {formatCurrency(totalAmount)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Cancellation Policy */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-gray-800 mb-2">
              Cancellation Policy
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              Cancellation allowed up to 24 hours before check-in.
            </p>
            <a
              href="#"
              className="text-primary text-sm underline font-medium hover:text-primary-dark"
            >
              Read StayTransit Terms & Conditions
            </a>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {/* Temporarily commented out - Download Invoice */}
            {/*
                            <button
                                onClick={handleInvoiceDownload}
                                className="btn-primary flex items-center justify-center gap-2"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Download Invoice
                            </button>
                            */}

            <button
              onClick={handleGoHome}
              className="btn-primary w-full sm:w-auto"
            >
              Continue Exploring
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReservationSuccess;
