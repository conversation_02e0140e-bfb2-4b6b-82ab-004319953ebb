import { MapPin, Plane, PlaneLanding } from "lucide-react";
import { type IPropertyDetails, PremisesType } from "@/types";

const DetailsBanner = ({
  propertyDetails,
}: {
  propertyDetails: IPropertyDetails;
}) => {
  return (
    <div className="">
      <div className="flex flex-col lg:flex-row gap-0">
        {/* Image Section */}
        <div className="lg:w-1/2 relative">
          <img
            src={propertyDetails.businessLogo}
            alt="Happy Stays property"
            className="w-full h-64 lg:h-96 object-contain rounded-xl"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent lg:hidden"></div>
        </div>

        <div className="lg:w-1/2 p-6 lg:p-8">
          <div className="mb-6">
            <h1 className="text-2xl lg:text-3xl font-bold mb-3">
              {propertyDetails.propertyId.name}
            </h1>

            {/* Location Info */}
            <div className="space-y-2 mb-2">
              <div className="flex items-center text-gray-500 gap-2">
                <MapPin className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {propertyDetails.propertyId.address.address1}
                </span>
              </div>

              <div className="flex items-center">
                <div
                  className={`px-6 py-2.5 text-white rounded-full mr-3 flex items-center gap-2 ${propertyDetails.propertyId.premisesType.toLowerCase() === PremisesType.AIRSIDE ? "bg-blue-600" : "bg-red-600"}`}
                >
                  {propertyDetails.propertyId.premisesType.toLowerCase() ===
                  PremisesType.AIRSIDE ? (
                    <Plane className="w-5 h-5" />
                  ) : (
                    <PlaneLanding className="w-5 h-5" />
                  )}
                  <span className="text-sm ">
                    {propertyDetails.propertyId.premisesType}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              About this property
            </h3>
            <p className="text-gray-600 leading-relaxed text-sm lg:text-base">
              {propertyDetails.aboutProperty}
            </p>
          </div>

          {/* CTA Button */}
          <button className={"btn-primary w-full"}>Browse Packages</button>
        </div>
      </div>
    </div>
  );
};

export default DetailsBanner;
