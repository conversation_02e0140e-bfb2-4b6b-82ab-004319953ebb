import type { ExploreData } from "@/components/explore-card/type.ts";

export const exploreData: ExploreData[] = [
  {
    id: "1",
    title: "Cozy Mountain Cabin",
    description:
      "Escape to a peaceful cabin in the mountains with stunning views and fresh air.",
    image: "https://picsum.photos/200/300?random=1",
    price: 120,
  },
  {
    id: "2",
    title: "Beachfront Bungalow",
    description:
      "Relax in a beautiful bungalow right on the beach with direct ocean access.",
    image: "https://picsum.photos/200/300?random=2",
    price: 200,
  },
  {
    id: "3",
    title: "Modern City Apartment",
    description:
      "Stay in the heart of the city with easy access to restaurants, shopping, and nightlife.",
    image: "https://picsum.photos/200/300?random=3",
    price: 150,
  },
  {
    id: "4",
    title: "Forest Treehouse",
    description:
      "Live among the trees in this unique treehouse nestled in a tranquil forest.",
    image: "https://picsum.photos/200/300?random=4",
    price: 180,
  },
  {
    id: "5",
    title: "Desert Dome Retreat",
    description:
      "Experience the calm of the desert in a stylish geodesic dome under the stars.",
    image: "https://picsum.photos/200/300?random=5",
    price: 130,
  },
];
