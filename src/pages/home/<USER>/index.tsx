import Header from "@/components/header";
import Search from "@/components/search";

const Banner = () => {
  return (
    <div className="relative h-[90vh] md:h-[75vh] w-full">
      <div className="absolute inset-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
        >
          <source src="/banner.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div
          className="absolute inset-0"
          style={
            {
              // backgroundImage: `linear-gradient(to bottom right, rgba(0,0,0,0.45), rgba(0,0,0,0.45))`,
            }
          }
        >
          <div className="p-10">
            <Header />
          </div>
          <div className="absolute top-64 bottom-50 left-0 right-0">
            <Search />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
