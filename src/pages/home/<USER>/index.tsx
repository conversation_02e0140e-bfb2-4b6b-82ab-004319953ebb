import CategoryCard from "@/components/category-card";
import { categoryData } from "@/pages/home/<USER>/sample-data.ts";

const Category = () => {
  return (
    <>
      <div
        className={"p-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"}
      >
        {categoryData.map((data) => (
          <div className={"w-full"} key={data.title}>
            <CategoryCard data={data} />
          </div>
        ))}
      </div>
    </>
  );
};

export default Category;
