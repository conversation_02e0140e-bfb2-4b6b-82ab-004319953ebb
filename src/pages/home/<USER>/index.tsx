import ExploreCard from "@/components/explore-card";
import { exploreData } from "@/pages/home/<USER>/sample-data.ts";

const Explore = () => {
  return (
    <div className={"p-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"}>
      {exploreData.map((data) => (
        <div key={data.id}>
          <ExploreCard data={data} />
        </div>
      ))}
    </div>
  );
};

export default Explore;
