import type { ServiceTabsProps } from "@/pages/search/service-tabs/types.ts";

const ServiceTabs = ({ activeTab, onTabChange }: ServiceTabsProps) => {
  const tabs = [
    { id: "hourly", label: "Hourly Stays" },
    { id: "nap", label: "Nap Capsules" },
    { id: "workspace", label: "Workspaces" },
    { id: "showers", label: "Showers" },
    { id: "kids", label: "Kids Area" },
  ];

  return (
    <div className="">
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-1 flex overflow-x-auto scrollbar-hide">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              relative px-4 md:px-6 py-2 mx-1 first:ml-0 last:mr-0
              text-sm font-medium flex-shrink-0 
              transition-all duration-300 ease-in-out
              whitespace-nowrap rounded-xl
              focus:outline-none focus:ring-1 focus:ring-gray-300 focus:ring-opacity-50
              ${
                activeTab === tab.id
                  ? "bg-white text-black shadow-lg transform scale-[1.02] ring-0.5 ring-gray-100"
                  : "bg-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50 hover:shadow-sm"
              }
            `}
          >
            <span className="relative z-10">{tab.label}</span>
            {activeTab === tab.id && (
              <div className="absolute inset-0 bg-gradient-to-r from-white via-gray-50 to-white rounded-xl opacity-60" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ServiceTabs;
