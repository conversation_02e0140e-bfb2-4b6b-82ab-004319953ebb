import { type ComponentType, type SVGProps, useState } from "react";
import { CreditCard, Filter, Luggage, Plane, Shield, X } from "lucide-react";
import type { FiltersSidebarProps } from "./types";

type DurationOption = { id: string; label: string; checked?: boolean };
type ClosestOption = {
  id: string;
  label: string;
  icon: ComponentType<SVGProps<SVGSVGElement>>;
};

type FilterContentProps = {
  priceRange: [number, number];
  setPriceRange: (_range: [number, number]) => void;
  filterOptions: {
    duration: DurationOption[];
    closestTo: ClosestOption[];
  };
};

const FiltersSidebar = ({
  filters: _filters,
  onFilterChange: _onFilterChange,
}: FiltersSidebarProps) => {
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 2000]);
  const [isOpen, setIsOpen] = useState(false);

  const filterOptions: FilterContentProps["filterOptions"] = {
    duration: [
      { id: "1-3hrs", label: "1-3 hrs", checked: true },
      { id: "3-6hrs", label: "3-6 hrs", checked: true },
      { id: "6+hrs", label: "6+ hrs" },
    ],
    closestTo: [
      { id: "boarding", label: "Boarding gate", icon: Plane },
      { id: "security", label: "Security", icon: Shield },
      { id: "checkin", label: "Check-in Counters", icon: CreditCard },
      { id: "baggage", label: "Baggage Claim", icon: Luggage },
    ],
  };

  return (
    <>
      {/* Mobile Filter Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="md:hidden  w-full bg-white border border-gray-200 rounded-xl p-3 mb-4 flex items-center justify-center gap-2 shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50"
      >
        <Filter className="w-4 h-4 text-gray-600" />
        <span className="font-medium text-gray-900">Filters</span>
      </button>

      {/* Mobile Overlay */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 bg-opacity-10 z-50 backdrop-blur-sm ">
          <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="p-4 border-b border-gray-100 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="p-4">
              <FilterContent
                priceRange={priceRange}
                setPriceRange={setPriceRange}
                filterOptions={filterOptions}
              />
            </div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <div className="hidden md:block w-80 bg-white p-6 rounded-2xl shadow-md border border-gray-100 h-fit sticky top-6">
        <h3 className="text-lg font-semibold mb-6 text-gray-900">Filters</h3>
        <FilterContent
          priceRange={priceRange}
          setPriceRange={setPriceRange}
          filterOptions={filterOptions}
        />
      </div>
    </>
  );
};

const FilterContent = ({
  priceRange,
  setPriceRange,
  // filterOptions,
}: FilterContentProps) => {
  return (
    <div className="space-y-6">
      {/* Price Range */}
      <div className="bg-gray-50 p-4 rounded-xl">
        <h4 className="font-medium mb-4 text-gray-900">Price Range</h4>
        <div className="flex gap-3 mb-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Min"
              className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm bg-white focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 focus:border-transparent transition-all duration-200"
              value={`$${priceRange[0]}`}
              readOnly
            />
          </div>
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Max"
              className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm bg-white focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 focus:border-transparent transition-all duration-200"
              value={`$${priceRange[1]}+`}
              readOnly
            />
          </div>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="2000"
            value={priceRange[1]}
            onChange={(e) =>
              setPriceRange([priceRange[0], parseInt(e.target.value)])
            }
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
        </div>
      </div>

      {/* Duration */}
      {/*<div className="bg-gray-50 p-4 rounded-xl">*/}
      {/*  <h4 className="font-medium mb-4 text-gray-900">Duration</h4>*/}
      {/*  <div className="space-y-3">*/}
      {/*    {filterOptions.duration.map((option) => (*/}
      {/*      <label*/}
      {/*        key={option.id}*/}
      {/*        className="flex items-center gap-3 cursor-pointer group"*/}
      {/*      >*/}
      {/*        <input*/}
      {/*          type="checkbox"*/}
      {/*          defaultChecked={option.checked}*/}
      {/*          className="w-4 h-4 text-gray-900 bg-white border-2 border-gray-300 rounded focus:ring-gray-300 focus:ring-2 transition-all duration-200"*/}
      {/*        />*/}
      {/*        <span className="text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200">*/}
      {/*          {option.label}*/}
      {/*        </span>*/}
      {/*      </label>*/}
      {/*    ))}*/}
      {/*  </div>*/}
      {/*</div>*/}

      {/* Closest To */}
      {/*<div className="bg-gray-50 p-4 rounded-xl">*/}
      {/*  <h4 className="font-medium mb-4 text-gray-900">Closest To</h4>*/}
      {/*  <div className="space-y-3">*/}
      {/*    {filterOptions.closestTo.map((option) => (*/}
      {/*      <label*/}
      {/*        key={option.id}*/}
      {/*        className="flex items-center gap-3 cursor-pointer group hover:bg-white hover:shadow-sm rounded-lg p-2 -m-2 transition-all duration-200"*/}
      {/*      >*/}
      {/*        <input*/}
      {/*          type="checkbox"*/}
      {/*          className="w-4 h-4 text-gray-900 bg-white border-2 border-gray-300 rounded focus:ring-gray-300 focus:ring-2 transition-all duration-200"*/}
      {/*        />*/}
      {/*        <div className="p-1 bg-white rounded-md group-hover:bg-gray-100 transition-colors duration-200">*/}
      {/*          <option.icon className="w-4 h-4 text-gray-600" />*/}
      {/*        </div>*/}
      {/*        <span className="text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200">*/}
      {/*          {option.label}*/}
      {/*        </span>*/}
      {/*      </label>*/}
      {/*    ))}*/}
      {/*  </div>*/}
      {/*</div>*/}
    </div>
  );
};

export default FiltersSidebar;
