import { MapPin } from "lucide-react";
import type { HotelCardProps } from "./types";
import { useNavigate, useSearchParams } from "react-router";

const HotelCard = ({ property }: HotelCardProps) => {
  const [serachParams] = useSearchParams();
  const navigate = useNavigate();
  const onClick = () => {
    navigate({
      pathname: `/property/${property._id}`,
      search: serachParams.toString(),
    });
  };
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden mb-4">
      <div className="">
        <div className={"p-2"}>
          <img
            src={property.logo}
            alt="Hotel room"
            className="w-full h-48 object-contain rounded-lg"
          />
        </div>

        {/* Content */}
        <div className="flex-1 p-2 flex flex-col justify-between">
          <div>
            <h3 className="text-lg md:text-lg font-semibold text-gray-900">
              {property.name}
            </h3>
            <div className="flex items-center gap-2 text-gray-500 mb-4 md:mb-1">
              <MapPin className="w-6 h-6" />
              <span className="text-xs truncate ellipsis ">
                {property.address.address1}
              </span>
            </div>
          </div>

          {/* Pricing and Button */}
          <div className="flex items-center justify-between gap-3 pt-2">
            <div className="flex flex-col">
              <span className="text-xs uppercase tracking-wide text-gray-500">
                Starts From
              </span>
              <span className="text-2xl font-bold text-gray-900">
                ${property.basePrice}
              </span>
            </div>

            <button
              className="w-auto bg-blue-600 text-white px-4 py-2  rounded-lg font-medium hover:bg-blue-700 transition-colors order-1 md:order-2"
              onClick={onClick}
            >
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelCard;
