import { useEffect, useState } from "react";
import { useSearchParams } from "react-router";
import SearchHeader from "./search-header";
import ServiceTabs from "./service-tabs";
import FiltersSidebar from "./filters-sidebar";
import HotelCard from "./hotel-card";
import Header from "@/components/header";
import AirportLoader from "@/components/loader";
import { getIntervalTime, utcInIso } from "@/utils";
import useAxios from "@/hooks/useAxios.tsx";
import { useToast } from "@/hooks/useToast";
import type { IProperty } from "@/types";

// Main Search Page Component
const SearchPage = () => {
  const {
    loading: isPropertiesLoading,
    data: properties,
    request: getProperties,
    error: propertiesError,
  } = useAxios<IProperty[]>();

  const toast = useToast();
  const [activeTab, setActiveTab] = useState("hourly");
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  const [searchParams] = useSearchParams();
  const airportLocation = searchParams.get("location") ?? "";
  const checkIn = searchParams.get("checkIn") ?? new Date();
  const checkOut = searchParams.get("checkOut") ?? getIntervalTime(new Date());

  useEffect(() => {
    const getPropertiesData = async () => {
      await getProperties({
        url: "/properties/search",
        method: "GET",
        params: {
          locationId: airportLocation,
          startDateTime: utcInIso(checkIn),
          endDateTime: utcInIso(checkOut),
        },
      });
    };
    getPropertiesData();
  }, [airportLocation, checkIn, checkOut]);

  useEffect(() => {
    if (propertiesError) {
      toast.show(propertiesError, { type: "error" });
    }
  }, [propertiesError]);

  if (isPropertiesLoading)
    return <AirportLoader message="Finding the best services for you..." />;

  return (
    <>
      <Header />
      <div className="min-h-screen  p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <SearchHeader />

          <div className="flex flex-col md:flex-row gap-4 md:gap-6">
            <FiltersSidebar filters={filters} onFilterChange={setFilters} />

            <div className="flex-1 space-y-4">
              <ServiceTabs activeTab={activeTab} onTabChange={setActiveTab} />
              <div className="space-y-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {properties &&
                  properties.map((property) => (
                    <HotelCard key={property._id} property={property} />
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchPage;
