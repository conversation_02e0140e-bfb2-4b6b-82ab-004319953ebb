import type { FieldErrors } from "react-hook-form";

export const getNestedError = (
  errors: FieldErrors,
  name: string,
): FieldErrors | undefined => {
  return name.split(".").reduce<FieldErrors | undefined>((obj, key) => {
    if (!obj) return undefined;

    if (key.includes("[")) {
      const [arrayName, index] = key.split(/[[\]]/).filter(Boolean);
      const nestedArray = obj[arrayName];
      if (Array.isArray(nestedArray)) {
        return nestedArray[parseInt(index, 10)] as FieldErrors;
      }
      return undefined;
    }

    return obj[key] as FieldErrors;
  }, errors);
};
