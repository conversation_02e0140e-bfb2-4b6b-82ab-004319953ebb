export const getIsoDate = (date: Date | string) => {
  if (typeof date === "string") {
    return new Date(date).toISOString();
  }
  return date.toISOString();
};

export const getUtcDate = (date: Date | string) => {
  if (typeof date === "string") {
    return new Date(date).toUTCString();
  }
  return date.toUTCString();
};

export const utcInIso = (date: Date | string) => {
  return getIsoDate(getUtcDate(date));
};

export const getIntervalTime = (
  dateTime: string | Date,
  interval: number = 60,
) => {
  const intervalInMs = interval * 60 * 1000;
  const current = new Date(dateTime);
  return new Date(current.getTime() + intervalInMs);
};

export const getFuture15MinuteInterval = (dateTime: string | Date) => {
  const current = new Date(dateTime);
  const minutes = current.getMinutes();
  const roundedMinutes = Math.ceil(minutes / 15) * 15;
  current.setMinutes(roundedMinutes);
  return current;
};
