import type { StylesConfig } from "react-select";
import type { SelectOption } from "@/pages/search/search-header/types.ts";

export const selectStyles: StylesConfig<SelectOption, false> = {
  singleValue: (provided) => ({
    ...provided,
    color: "white",
  }),
  placeholder: (provided) => ({
    ...provided,
    color: "white",
  }),
  option: (provided, state) => ({
    ...provided,
    color: state.isSelected ? "#fff" : "#6B7280",
    backgroundColor: state.isSelected ? "#2563EB" : "#fff",
    "&:hover": {
      backgroundColor: state.isSelected ? "#1E40AF" : "#F3F4F6",
      transition: "background-color 0.3s ease",
    },
  }),
  control: (provided) => ({
    ...provided,
    color: "#6B7280",
    border: "none",
    boxShadow: "none",
    backgroundColor: "transparent",
    "&:hover": {
      border: "none",
    },
  }),
  input: (provided) => ({
    ...provided,
    color: "white",
    backgroundColor: "transparent",
  }),
  menu: (provided) => ({
    ...provided,
    zIndex: 1000,
  }),
  menuList: (provided) => ({
    ...provided,
    maxHeight: "180px",
    zIndex: 1000,
  }),
};
