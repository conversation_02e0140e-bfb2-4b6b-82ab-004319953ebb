import React from "react";
import type { BookingCardProps } from "@/components/booking-card/type.ts";

const BookingItem: React.FC<BookingCardProps> = ({ reservation }) => {
  return (
    <div className="border rounded-lg p-4 mb-4 flex justify-between items-center">
      <div>
        <h3 className="font-bold">{reservation.propertyId.name}</h3>
        <p className="text-gray-600">
          {reservation.propertyId.address.address1}
        </p>
        <p className="text-gray-600">
          {new Date(reservation.items[0].startDateTime).toLocaleDateString()}
        </p>
      </div>
      <div className="flex items-center">
        <span className="bg-green-100 text-green-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded">
          Confirmed
        </span>
        <span className="text-lg font-semibold">${reservation.grandTotal}</span>
      </div>
    </div>
  );
};

export default BookingItem;
