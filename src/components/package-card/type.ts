import type { IPackage } from "@/types/Package.ts";

export type PackageData = {
  title: string;
  imageUrl: string;
  imageAlt: string;
  size: string;
  bedType: string;
  sleeps: number;
  amenities: string[];
  pricePerHour: number;
  taxesAndFees: number;
  viewMoreLink: string;
};

export type PackageCardProps = {
  packageData: IPackage;
  onPackageChange: (_pkg: IPackage, _type: "add" | "remove") => void;
  selectedPackages: IPackage[] | null;
};
