import React, { createContext, useCallback, useMemo, useState } from "react";

export type PopupOptions = {
  // Optional title for the popup header
  title?: string;
  // If true, clicking on the overlay will close the popup (default true)
  closeOnOverlay?: boolean;
  // If provided, pressing Escape will close the popup (default true)
  closeOnEsc?: boolean;
  // Optional width for the dialog
  width?: number | string;
  // Optional onClose callback
  onClose?: () => void;
};

export type PopupHandle = {
  id: string;
  close: () => void;
};

export type PopupContextValue = {
  // Show a popup with arbitrary React content. Returns a handle with close().
  show: (_content: React.ReactNode, _options?: PopupOptions) => PopupHandle;
  // Close the currently open popup (if any)
  close: () => void;
  // Whether a popup is open
  isOpen: boolean;
};

export const PopupContext = createContext<PopupContextValue | undefined>(
  undefined,
);

let __popupCounter = 0;

export const PopupProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [popup, setPopup] = useState<{
    id: string;
    content: React.ReactNode;
    options: PopupOptions;
  } | null>(null);

  const internalClose = useCallback(() => {
    setPopup((prev) => {
      if (!prev) return null;
      prev.options?.onClose?.();
      return null;
    });
  }, []);

  const show = useCallback(
    (content: React.ReactNode, options?: PopupOptions): PopupHandle => {
      const id = `popup_${++__popupCounter}`;
      setPopup({
        id,
        content,
        options: { closeOnOverlay: true, closeOnEsc: true, ...options },
      });
      return { id, close: internalClose };
    },
    [internalClose],
  );

  const close = internalClose;

  const value = useMemo(
    () => ({ show, close, isOpen: !!popup }),
    [show, close, popup],
  );

  // Handle Escape key
  React.useEffect(() => {
    if (!popup) return;
    const handler = (e: KeyboardEvent) => {
      if (e.key === "Escape" && popup.options.closeOnEsc !== false) {
        internalClose();
      }
    };
    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, [popup, internalClose]);

  return (
    <PopupContext.Provider value={value}>
      {children}
      {/* Popup portal/container */}
      {popup && (
        <div
          role="dialog"
          aria-modal="true"
          style={{
            position: "fixed",
            inset: 0,
            background: "rgba(0,0,0,0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 10000,
          }}
          onClick={() => {
            if (popup.options.closeOnOverlay !== false) internalClose();
          }}
        >
          <div
            style={{
              background: "#fff",
              borderRadius: 8,
              width: popup.options.width ?? 480,
              maxWidth: "90vw",
              maxHeight: "85vh",
              overflow: "auto",
              boxShadow:
                "0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1)",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {(popup.options.title || popup.options.onClose) && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "12px 16px",
                  borderBottom: "1px solid #eee",
                }}
              >
                <div style={{ fontWeight: 600 }}>{popup.options.title}</div>
                <button
                  onClick={internalClose}
                  aria-label="Close popup"
                  style={{
                    background: "transparent",
                    border: "none",
                    fontSize: 22,
                    lineHeight: 1,
                    cursor: "pointer",
                  }}
                >
                  ×
                </button>
              </div>
            )}
            <div style={{ padding: 16 }}>{popup.content}</div>
          </div>
        </div>
      )}
    </PopupContext.Provider>
  );
};
