import React from "react";
import type { ReviewCardProps } from "@/components/review-card/type.ts";
import { Star } from "lucide-react";

const ReviewCard: React.FC<ReviewCardProps> = ({ review }) => {
  const { name, image, rating, description } = review;
  const stars = Array.from({ length: 5 }, (_, index) => (
    <Star className={"w-5 h-5 text-yellow-500"} key={index} />
  )).slice(0, rating);

  return (
    <div className="bg-white shadow-lg rounded-lg border border-gray-100">
      <div className="flex items-center px-6 py-4">
        <img
          className="w-12 h-12 rounded-full mr-4"
          src={image || "https://via.placeholder.com/50"}
          alt="Profile"
        />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
          <div className="flex">{stars}</div>
        </div>
      </div>
      <p className="text-gray-500 italic px-4 py-1 line-clamp-2">
        {description}
      </p>
    </div>
  );
};

export default ReviewCard;
