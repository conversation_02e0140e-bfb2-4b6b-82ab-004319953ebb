import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLoginPopup } from '@/hooks/useLoginPopup';

const AuthDemo: React.FC = () => {
  const { user, signOut } = useAuth();
  const { openLoginPopup } = useLoginPopup();

  if (user) {
    return (
      <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-lg font-semibold text-green-800 mb-2">
          Welcome, {user.displayName || user.email || user.phoneNumber}!
        </h3>
        <p className="text-green-700 mb-4">You are successfully logged in.</p>
        <button
          onClick={signOut}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Sign Out
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-lg font-semibold text-blue-800 mb-2">
        Authentication Demo
      </h3>
      <p className="text-blue-700 mb-4">
        Click the button below to test the login popup with phone, email, and Google authentication.
      </p>
      <button
        onClick={() => openLoginPopup()}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Open Login Popup
      </button>
    </div>
  );
};

export default AuthDemo;
