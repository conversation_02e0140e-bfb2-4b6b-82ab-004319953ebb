import React, { useState } from 'react';
import { Mail, Chrome, ArrowLeft, Loader2 } from 'lucide-react';
import { authService } from '@/services/authService';
import type { EmailAuthData } from '@/services/authService';
import { useToast } from '@/hooks/useToast';
import { usePopup } from '@/hooks/usePopup';
import PhoneLogin from './PhoneLogin';
import EmailLogin from './EmailLogin';

type AuthMethod = 'phone' | 'email' | 'google';

interface LoginPopupProps {
  onSuccess?: () => void;
}

const LoginPopup: React.FC<LoginPopupProps> = ({ onSuccess }) => {
  const [activeMethod, setActiveMethod] = useState<AuthMethod>('phone');
  const [loading, setLoading] = useState(false);
  const toast = useToast();
  const popup = usePopup();

  // Handle Google Sign In
  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      await authService.signInWithGoogle();
      toast.show('Successfully signed in with Google!', { type: 'success' });
      popup.close();
      onSuccess?.();
    } catch (error: any) {
      console.error('Google sign in error:', error);
      toast.show(error.message || 'Failed to sign in with Google', { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Handle Email Authentication
  const handleEmailAuth = async (data: EmailAuthData, isSignUp: boolean) => {
    setLoading(true);
    try {
      if (isSignUp) {
        await authService.signUpWithEmail(data);
      } else {
        await authService.signInWithEmail(data);
      }

      toast.show(`Successfully ${isSignUp ? 'signed up' : 'signed in'}!`, { type: 'success' });
      popup.close();
      onSuccess?.();
    } catch (error: any) {
      console.error('Email auth error:', error);

      // Extract error message from API response
      let errorMessage = 'Authentication failed';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Handle Phone Authentication Success
  const handlePhoneSuccess = () => {
    toast.show('Successfully signed in with phone!', { type: 'success' });
    popup.close();
    onSuccess?.();
  };

  const renderAuthMethod = () => {
    switch (activeMethod) {
      case 'phone':
        return <PhoneLogin onSuccess={handlePhoneSuccess} loading={loading} setLoading={setLoading} />;
      case 'email':
        return <EmailLogin onSubmit={handleEmailAuth} loading={loading} />;
      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h2>
        <p className="text-gray-600">Sign in to your account to continue</p>
      </div>

      {/* Back Button (only show when not on phone method) */}
      {activeMethod !== 'phone' && (
        <button
          onClick={() => setActiveMethod('phone')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to phone login
        </button>
      )}

      {/* Auth Method Content */}
      <div className="mb-6">
        {renderAuthMethod()}
      </div>

      {/* Alternative Methods (only show on phone method) */}
      {activeMethod === 'phone' && (
        <div className="space-y-3">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          {/* Email Option */}
          <button
            onClick={() => setActiveMethod('email')}
            disabled={loading}
            className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Mail className="w-5 h-5 text-gray-600" />
            <span className="text-gray-700 font-medium">Continue with Email</span>
          </button>

          {/* Google Option */}
          <button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 text-gray-600 animate-spin" />
            ) : (
              <Chrome className="w-5 h-5 text-gray-600" />
            )}
            <span className="text-gray-700 font-medium">Continue with Google</span>
          </button>
        </div>
      )}

      {/* Terms */}
      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500">
          By continuing, you agree to our{' '}
          <a href="#" className="text-blue-600 hover:underline">Terms of Service</a>
          {' '}and{' '}
          <a href="#" className="text-blue-600 hover:underline">Privacy Policy</a>
        </p>
      </div>
    </div>
  );
};

export default LoginPopup;
