import React, {useEffect, useRef, useState} from 'react';
import {Loader2, Phone} from 'lucide-react';
import {authService} from '@/services/authService';
import {useToast} from '@/hooks/useToast';
import type {ConfirmationResult} from 'firebase/auth';

interface PhoneLoginProps {
    onSuccess: () => void;
    loading: boolean;
    setLoading: (_loading: boolean) => void;
}

const PhoneLogin: React.FC<PhoneLoginProps> = ({onSuccess, loading, setLoading}) => {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [verificationCode, setVerificationCode] = useState('');
    const [step, setStep] = useState<'phone' | 'verification'>('phone');
    const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
    const [countdown, setCountdown] = useState(0);
    const toast = useToast();
    const recaptchaRef = useRef<HTMLDivElement>(null);

    // Countdown timer for resend
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (countdown > 0) {
            timer = setTimeout(() => setCountdown(countdown - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [countdown]);

    // Format phone number as user types
    const formatPhoneNumber = (value: string) => {
        const phoneNumber = value.replace(/[^\d]/g, '');
        const phoneNumberLength = phoneNumber.length;

        if (phoneNumberLength < 4) return phoneNumber;
        if (phoneNumberLength < 7) {
            return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
        }
        return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatPhoneNumber(e.target.value);
        setPhoneNumber(formatted);
    };

    // Send verification code
    const sendVerificationCode = async () => {
        if (!phoneNumber.trim()) {
            toast.show('Please enter a phone number', {type: 'error'});
            return;
        }

        // Convert formatted phone to E.164 format
        const cleanPhone = phoneNumber.replace(/[^\d]/g, '');
        const e164Phone = cleanPhone.length === 10 ? `+91${cleanPhone}` : `+${cleanPhone}`;


        setLoading(true);
        try {
            // Setup recaptcha
            const recaptchaVerifier = authService.setupRecaptcha('recaptcha-container');

            // Send verification code
            const confirmation = await authService.sendPhoneVerification(e164Phone, recaptchaVerifier);
            setConfirmationResult(confirmation);
            setStep('verification');
            setCountdown(60); // 60 seconds countdown
            toast.show('Verification code sent!', {type: 'success'});
        } catch (error: unknown) {
            console.error('Phone verification error:', error);
            const errorObj = error as Error;
            toast.show(errorObj.message || 'Failed to send verification code', {type: 'error'});
            authService.cleanupRecaptcha();
        } finally {
            setLoading(false);
        }
    };

    // Verify code
    const verifyCode = async () => {
        if (!verificationCode.trim()) {
            toast.show('Please enter the verification code', {type: 'error'});
            return;
        }

        if (!confirmationResult) {
            toast.show('No verification in progress', {type: 'error'});
            return;
        }

        setLoading(true);
        try {
            await authService.verifyPhoneCode(confirmationResult, verificationCode);
            onSuccess();
        } catch (error: unknown) {
            console.error('Code verification error:', error);
            const errorObj = error as Error;
            toast.show(errorObj.message || 'Invalid verification code', {type: 'error'});
        } finally {
            setLoading(false);
        }
    };

    // Resend code
    const resendCode = async () => {
        if (countdown > 0) return;
        await sendVerificationCode();
    };

    if (step === 'verification') {
        return (
            <div className="space-y-4">
                <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Enter Verification Code</h3>
                    <p className="text-gray-600 text-sm">
                        We sent a code to {phoneNumber}
                    </p>
                </div>

                <div>
                    <input
                        type="text"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                        placeholder="Enter 6-digit code"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-lg tracking-widest"
                        maxLength={6}
                        autoComplete="one-time-code"
                    />
                </div>

                <button
                    onClick={verifyCode}
                    disabled={loading || !verificationCode.trim()}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                >
                    {loading ? (
                        <>
                            <Loader2 className="w-5 h-5 animate-spin"/>
                            Verifying...
                        </>
                    ) : (
                        'Verify Code'
                    )}
                </button>

                <div className="text-center">
                    <button
                        onClick={resendCode}
                        disabled={countdown > 0}
                        className="text-blue-600 hover:text-blue-700 text-sm disabled:text-gray-400 disabled:cursor-not-allowed"
                    >
                        {countdown > 0 ? `Resend code in ${countdown}s` : 'Resend code'}
                    </button>
                </div>

                <button
                    onClick={() => {
                        setStep('phone');
                        setVerificationCode('');
                        authService.cleanupRecaptcha();
                    }}
                    className="w-full text-gray-600 hover:text-gray-900 text-sm"
                >
                    Change phone number
                </button>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"/>
                <input
                    type="tel"
                    value={phoneNumber}
                    onChange={handlePhoneChange}
                    placeholder="(*************"
                    className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    maxLength={14}
                />
            </div>

            <button
                onClick={sendVerificationCode}
                disabled={loading || !phoneNumber.trim()}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
            >
                {loading ? (
                    <>
                        <Loader2 className="w-5 h-5 animate-spin"/>
                        Sending...
                    </>
                ) : (
                    'Send Verification Code'
                )}
            </button>

            {/* Recaptcha container */}
            <div id="recaptcha-container" ref={recaptchaRef}></div>
        </div>
    );
};

export default PhoneLogin;
