import * as React from "react";
import type { ExploreCardProps } from "@/components/explore-card/type.ts";

const ExploreCard: React.FC<ExploreCardProps> = ({ data }) => {
  return (
    <>
      <div
        className={
          "p-4 min-h-[400px] bg-white rounded-lg shadow-md  border border-gray-200"
        }
      >
        <h1 className={"text-lg"}>{data.title}</h1>
        <p className={"text-gray-400 text-sm mb-2"}>{data.description}</p>
        <img
          src={data.image}
          alt={data.title}
          className={"w-full h-60 rounded-xl object-cover"}
        />
        <div className={"flex justify-between items-center mt-4"}>
          <p className={"text-lg font-semibold"}>
            From <span className={"text-primary"}>${data.price}/hr</span>
          </p>
          <button className={"btn-outline"}>Explore</button>
        </div>
      </div>
    </>
  );
};
export default ExploreCard;
