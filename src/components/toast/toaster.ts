import type { Toast, ToastType } from "./ToasterProvider";

type Subscriber = (_toast: Toast) => void;

let __toastCounter = 0;
function generateId(): string {
  if (
    typeof crypto !== "undefined" &&
    typeof crypto.randomUUID === "function"
  ) {
    return crypto.randomUUID();
  }
  __toastCounter = (__toastCounter + 1) % 1_000_000;
  const rand = Math.floor(Math.random() * 1e6).toString(36);
  return `t_${Date.now().toString(36)}_${__toastCounter.toString(36)}_${rand}`;
}

class Toaster {
  private subs: Set<Subscriber> = new Set();

  subscribe(sub: Subscriber) {
    this.subs.add(sub);
    return () => this.subs.delete(sub);
  }

  push(message: string, options?: { type?: ToastType; duration?: number }) {
    const toast: Toast = {
      id: generateId(),
      message,
      type: options?.type ?? "info",
      duration: options?.duration ?? 4000,
    };
    this.subs.forEach((s) => s(toast));
  }

  info(message: string, duration?: number) {
    this.push(message, { type: "info", duration });
  }
  success(message: string, duration?: number) {
    this.push(message, { type: "success", duration });
  }
  error(message: string, duration?: number) {
    this.push(message, { type: "error", duration });
  }
  warning(message: string, duration?: number) {
    this.push(message, { type: "warning", duration });
  }
}

export const toaster = new Toaster();
export default toaster;
