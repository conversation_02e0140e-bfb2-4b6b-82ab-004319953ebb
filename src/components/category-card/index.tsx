import * as React from "react";
import type { CategoryCardProps } from "@/components/category-card/type.ts";

const CategoryCard: React.FC<CategoryCardProps> = ({ data }) => {
  return (
    <div
      className={"w-full px-6 py-4 border border-gray-200 rounded-xl bg-white"}
    >
      <h1 className={"text-lg"}>{data.title}</h1>
      <p className={"text-sm text-gray-400 mb-2"}>{data.description}</p>
      <img
        src={data.image}
        alt={data.title}
        className={"w-full h-60 rounded-xl object-cover mb-2"}
      />
      <button className={"btn-primary w-full"}>{data.buttonText}</button>
    </div>
  );
};

export default CategoryCard;
