{"name": "staytransit-guest-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "precommit": "npm run lint:fix  && npm run format", "prepare": "husky"}, "dependencies": {"@stripe/react-stripe-js": "^3.9.2", "@tailwindcss/vite": "^4.1.12", "axios": "^1.11.0", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-datepicker": "^8.7.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-router": "^7.8.2", "react-select": "^5.10.2", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}