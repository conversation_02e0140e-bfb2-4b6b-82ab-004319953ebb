import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';

export default tseslint.config(
    { ignores: ['dist'] },
    {
        extends: [js.configs.recommended, ...tseslint.configs.recommended],
        files: ['**/*.{ts,tsx}'],
        languageOptions: {
            ecmaVersion: 2020,
            globals: globals.browser,
        },
        plugins: {
            'react-hooks': reactHooks,
            'react-refresh': reactRefresh,
        },
        rules: {
            ...reactHooks.configs.recommended.rules,
            'react-hooks/exhaustive-deps': 'off',
            // 'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
            'no-unused-vars': [
                'warn',
                {
                    argsIgnorePattern: '^_', // Ignore arguments (function parameters) starting with _
                    varsIgnorePattern: '^_', // Ignore variables starting with _
                    caughtErrorsIgnorePattern: '^_', // Ignore caught errors starting with _
                },
            ],
            'no-console': ['error', { allow: ['warn', 'error'] }],
            // If you're using TypeScript, you might need to configure @typescript-eslint/no-unused-vars as well
            '@typescript-eslint/no-unused-vars': [
                'error',
                {
                    argsIgnorePattern: '^_',
                    varsIgnorePattern: '^_',
                    caughtErrorsIgnorePattern: '^_',
                },
            ],
        },
    }
);
